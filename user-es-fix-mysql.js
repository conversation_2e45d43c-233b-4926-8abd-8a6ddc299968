import elasticsearch from 'elasticsearch';
import mysql from 'mysql2/promise';
import moment from 'moment';

const esClient = new elasticsearch.Client({
  hosts: [
    {
      host: 'es-cluster-1.impress-es.svc.cluster.fud3',
      port: '9200',
    },
    {
      host: 'es-cluster-2.impress-es.svc.cluster.fud3',
      port: '9200',
    },
    {
      host: 'es-cluster-3.impress-es.svc.cluster.fud3',
      port: '9200',
    },
    {
      host: 'es-cluster-4.impress-es.svc.cluster.fud3',
      port: '9200',
    },
    {
      host: 'es-cluster-5.impress-es.svc.cluster.fud3',
      port: '9200',
    },
  ],
  sniffOnStart: true,
  sniffOnConnectionFault: true,
  sniffInterval: 5000,
  requestTimeout: 60000,
  deadTimeout: 70000,
  maxRetries: 3,
  keepAlive: true,
  keepAliveInterval: 5000,
  log: {
    type: 'stdio',
    levels: ['error', 'warning'],
  },
});

const dbConfig = {
  host: 'kingshard.service.upyun',
  port: 9654,
  user: 'impress',
  password: 'Dmf2k84Edm',
  database: 'impress',
  connectionLimit: 8,
};

async function main() {
  const pool = mysql.createPool(dbConfig);

  try {
    // 首先测试连接
    const pingResponse = await esClient.ping();
    if (!pingResponse) {
      throw new Error('无法连接到 Elasticsearch');
    }
    console.log('成功连接到 Elasticsearch');

    const searchResponse = await esClient.search({
      index: 'users',
      body: {
        query: {
          bool: {
            should: [
              { match: { username: { query: 'yupoo', boost: 10 } } },
              { match: { nickname: { query: 'yupoo', boost: 2 } } },
              { match: { email: { query: 'yupoo' } } },
            ],
            minimum_should_match: 1,
          },
        },
        size: 10000,
      },
    });

    const esUsers = searchResponse.hits.hits;
    console.log(`Found ${esUsers.length} users in ES containing "yupoo"`);
    console.log('Total hits:', searchResponse.hits.total);

    esUsers.slice(0, 5).forEach((user) => {
      console.log('User:', {
        id: user._id,
        username: user._source.username,
        nickname: user._source.nickname,
        email: user._source.email,
      });
    });

    for (const esUser of esUsers) {
      const userId = esUser._id;
      console.log(userId);
      const [rows] = await pool.execute('SELECT id FROM users WHERE id = ?', [userId]);
      console.log(rows);

      if (rows.length === 0) {
        console.log(`Deleting user ${userId} from ES as it doesn't exist in MySQL`);
        // await esClient.delete({
        //   index: 'users',
        //   id: userId,
        // });
      }
    }

    console.log('Sync completed successfully');
  } catch (error) {
    console.error('Error during sync:', error);
  } finally {
    await pool.end();
  }
}

main();
