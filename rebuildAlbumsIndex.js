import elasticsearch from 'elasticsearch';
import mysql from 'mysql2/promise';
import moment from 'moment';

const userNotInShardTable_ = [
  38, 72, 111, 243, 250, 251, 263, 264, 273, 386, 411, 414, 433, 512, 540, 542, 579, 594, 672, 690, 698, 711, 725, 745,
  795, 833, 898, 911, 933, 1034, 1107, 1549, 1641, 1721, 1904, 1975, 1986, 2046, 2075, 2089, 2120, 2146, 2162, 2213,
  2254, 2646, 2761, 2768, 2851, 2855, 2859, 3096, 3127, 3313, 3401, 3826, 3986, 4063, 4088, 4256, 4395, 4538, 4643,
  4687, 4707, 5107, 5497, 5696, 5972, 6200, 6461, 6574, 6691, 7009, 7174, 7453, 7542, 7710, 7773, 7799, 7869, 7896,
  7919, 7926, 7981, 8033, 8390, 8430, 8886, 9037, 9590, 9775, 9798, 9976, 10092, 10275, 11015, 11120, 11217, 11599,
  12869, 12927, 13767, 13942, 14577, 14581, 14746, 14889, 15396, 15572, 15668, 15989, 16839, 17011, 17861, 17993, 18208,
  18273, 18387, 18537, 18737, 19446, 19495, 20036, 20491, 21130, 22013, 22124, 22126, 22434, 22464, 22499, 22501, 22542,
  25684, 25786, 25864, 26224, 26428, 26585, 26824, 27207, 27437, 27670, 27836, 28855, 29332, 30309, 30622, 30631, 30774,
  30891, 30913, 31113, 31345, 31346, 31348, 31443, 31517, 31720, 31836, 32622, 32657, 32661, 32744, 32825, 32962, 33097,
  33541, 33737, 33837, 34423, 34647, 35050, 35120, 35272, 36440, 36538, 36547, 36589, 36876, 36921, 37907, 37917, 38352,
  38355, 38846, 38849, 38976, 39000, 39221, 39574, 39688, 39893, 40236, 40240, 40290, 40404, 40722, 40729, 41025, 41702,
  41762, 42067, 42136, 43133, 43667, 44668, 45172, 45189, 45349, 45466, 45510, 45588, 46849, 47482, 47632, 48789, 49537,
  49735, 50328, 50460, 51171, 53792, 54168, 54215, 54339, 54474, 55173, 55313, 55517, 55549, 55940, 56066, 56940, 57107,
  57542, 57701, 57812, 58670, 58927, 59932, 60614, 60748, 60859, 60936, 61355, 61499, 62952, 64187, 64198, 64355, 65036,
  65491, 65957, 67061, 67898, 67962, 68089, 68766, 69246, 70070, 70171, 70334, 70486, 72099, 72534, 72812, 72988, 73207,
  75071, 75786, 75811, 76484, 77330, 79906, 79951, 80920, 82851, 83553, 83948, 84461, 84909, 85066, 86743, 87729, 87986,
  88198, 88378, 89042, 89357, 89379, 91674, 91799, 92634, 92761, 92798, 93736, 94315, 94375, 94778, 95053, 95161, 96970,
  97002, 97457, 98661, 98801, 99473, 99799, 100229, 100376, 100420, 100776, 100961, 101414, 102344, 103725, 103773,
  105154, 105401, 105853, 106275, 106506, 106878, 107506, 109216, 109714, 109775, 110972, 111210, 112189, 112199,
  112542, 113623, 113990, 114770, 115404, 116468, 117618, 118023, 118045, 118448, 119496, 120016, 121241, 122021,
  122518, 122652, 122660, 123962, 126314, 126587, 127094, 127452, 127532, 130063, 130433, 131564, 132994, 133955,
  134347, 134528, 134690, 135415, 136883, 138244, 138590, 141255, 142303, 142453, 142691, 143059, 143722, 143768,
  144322, 144439, 145035, 145252, 146916, 146955, 149127, 149308, 149820, 150275, 151531, 155101, 160112, 160695,
  161528, 161599, 161679, 162749, 162830, 168894, 177566, 183757, 193891, 197575, 198904, 214226, 231965, 235400,
  237158, 239024, 240509, 241056, 251847, 254852, 257795, 269030, 271151, 272933, 275302, 277420, 282438, 290981,
  291030, 291794, 292291, 295448, 297191, 297408, 301728, 2747326, 2771835, 2771852, 2772032, 3115710, 3370126, 3514129,
  3514152, 3514161, 3514170, 3514182, 3514205, 3514244, 3514246, 3514251, 3873091, 3873093, 3873112, 3873122, 3873129,
  3873130, 3873145, 3873157, 3873160, 3873167, 3873170, 3873175,
];
const userShardCalculationError_ = [[3635644, 4]];

const userNotInShardTable = new Set(userNotInShardTable_);
const userShardCalculationError = new Map(userShardCalculationError_);

// select bucket_name from ypflow where http_referer like '%amazon%' group by bucket_name;
const bucket_names = [
  'amzking',
  'hunshop',
  'xmzg',
  '3lan3',
  '457155',
  'szect',
  'vc8850',
  'lcp9111',
  'nadu',
  'allss998',
  'zcxyh001',
  'hsym',
  'ezmm777',
  'elvp',
  'tlex9',
  'mellowxq',
  'zzfs2020',
  'ymtx_v',
  'l-jc',
  'coco8735',
  'sunnyl',
  'heyulin',
  'topgal',
  'yuesir',
  'lazypop',
  'leiwuhen',
  'qzqnsdmy',
  'shopgn_v',
  'rchbin',
  '_daolian',
  'zysj617',
  'ading143',
  'akaj1234',
  'huang555',
  'yuu5111',
  '38667929',
  'lim6100',
  'sunnll',
  'yiyi456',
  'cwkk',
  'xgacr',
  'baida',
  'kauu',
  'od0805_v',
  'clyy001',
  'xxy999am',
  'zjq117',
  'sz3750',
  'xiehe798',
  'amzhzb',
  'hzb0321',
  'lwq7826',
  '001top',
  '88877759',
  'guyarns',
  'jpbeta',
  'kxbblt',
  'edfndso',
  'artpic',
  '24556398',
  'dre0369',
  'bo5566',
  'plei912',
  'yksus',
  '56pic95',
  'leafzhuo',
  'lhy1111',
  'a112347',
  'aaa02000',
  'yonshine',
  'aprillan',
  'jcfushi',
  'qq502308',
  'ww012332',
  '998.my',
  'six611',
  '79294128',
  'abcbbpet',
  'lushina',
  'jyst',
  'laojiu8',
  'xmsy',
  'sayeong',
  'ptxj',
  'pt7375_v',
  'blllue',
  'wssvsfjj',
  'shiqi456',
  'option2',
  'olzfs',
  'yibaiqin',
  'derr8900',
  'emanliu',
  'zoanoamz',
  '5mnbv',
  'fotomag',
  'sl40a',
  'whiteyiy',
  'a45601',
  'mh123456',
  '986863',
  'gyeolmi',
  'toxic98',
  '121cys12',
  'yq2124',
  'shenxing',
  'xxx-m',
  'cao02',
  'nonoloto',
  'h-hoover',
  'zjs9915',
  'auxmax',
  'cwhing14',
  'may1021',
  'giallino',
  'djy12345',
  'rikou',
  'elfkit',
  'zizi100',
  'wefs8888',
  '15233',
  'areting',
  'froyland',
  '96586',
  'lxj0858',
  'rrr1245',
  'xzp168',
  'jane208',
  'jiarenorg',
  '08302580771',
  'gash136_v',
  'caikuanqi',
  'suixinsuoyu9900',
  'qq451779009',
  'lelouchcczero',
  '415525688',
  'karliehuo',
  'wzq51wzq51',
  'tukujia65',
  'hyy199596',
  'ojianrong',
  'chujianyi',
  'xiaokong-729',
  '152262823',
  'jindingxieye',
  'qq148279770',
  'xinjiewei',
  'chunyuekeji',
  '1729989404',
  'tukujia258',
  '17706020308',
  'a270500590',
  'ypqiezi002',
  'hongxu2019',
  'stevenmmm_v',
  'limingxia123abc',
  'bangweini124',
  'sandra0611',
  'fanthuat_v',
  'baimei2002',
  'feilong7302',
  '1051843320',
  'hjas558smsmks',
  'qq1471402018',
  'adelezh-xu_v',
  'cjh58961538',
  'vinaderhom',
  '865913436',
  'ww123456a',
  'alexmak0223',
  'c867610801',
  'huangxuelin3',
  '236000153',
  'dree9200220',
  'kailuzhe888',
  'ririsheng2006',
  'jaychaoqun',
  'kangxiaoxiong',
  'carrier1219',
  '13432066661',
  'ym123456789',
  'xieerduo6688',
  'minixiepifa',
  '15160068535',
  'a13859867890',
  '1723689724',
  '271747114_v',
  'henrrybox',
  'hongshuaitiyu',
  'chengting0901',
  'qzone805171865_v',
  'yvonnesai',
  '979979sdsdsdsd',
  'wismar2008',
  'a00223300_v',
  'a15759934082',
  'sxc0118_v',
  'lianfa123',
  'tianzhilan001',
  'kimzon777',
  '2632958587',
  'kuailetongxie',
  'lin9989868',
  'ltdoremi_v',
  '840519153',
  'lvliang1027',
  'linweifeng0117',
  'chadchen0522',
  'shuaige003',
  'xiedaxieye',
  'limin9078',
  '1143710497',
  'hemlock-1',
  'zhicong168',
  'mulanxiewu_v',
  'zsw739053335',
  'syyamazon',
  'xiaomaibu99',
  'rangyirang1005',
  'aa18350748823',
  'feite2009',
  '1013969117',
  'kimzon888',
  '13332505905',
  '372609600',
  'elecrealm',
  'xuanzhuan88',
  'yingxiangsui',
  '778639216',
  'zcyhchenhui',
  'hnchsoxawe',
  'laycher_v',
  'arasiyama',
  'denegob753',
  '2464940050',
  '5869192_v',
  'maxiaodong0530',
  'alt-ctrl1108_v',
  '13702888115',
  '565058439_v',
  'fff147258369',
  'rutenbuy1',
  'pft1084790592',
  'junding1815',
  'weibo8888',
  'wanmaxiuxianxie',
  'chris4589',
  '247061528',
  'lianzumaoyi888_v',
  'qrqr-f552',
  'pushupiaoshu',
  '173245548',
  '282464282',
  'wxfffff00011',
  'wuxiaolan171748',
  'forrest071',
  'wenwen098789',
  'qzone958005611_v',
  'wangluo18_v',
  'sekirochen',
  'laobanlaile',
  '13489912406',
  'winniesi_v',
  'chenxiangliang',
  'swd051582762451',
  'yzz123321',
  'chxmy-168',
  '18059966606',
  'wuxiaoqiang2011',
  'd62021-02',
  'huangrui0',
  'gongxurong',
  'gracelynn326',
  'dennyhan86',
  'yushan2019',
  'ladeng911',
  'hhq19900701',
  'dehduewdewu',
  'yuyuedamao',
  'nicole0920',
  'dingshiwen520',
  'ab262042330',
  'adidas5520',
  'xiebazhekoudian',
  'seoria-009',
  '**********',
  'lanyinpian',
  'zhulin123456',
  '381141298',
  'jomedesign',
  '644510284',
  'freemandd',
  'cool-converse',
  'ywq2000_v',
  'xuzhijing13',
  'kelly1992',
  'lllinda66',
  '**********',
  'zzc152003',
  'meimong2019',
  'kimzon999',
  'qingchuanliushui168_v',
  'qzone1783277383_v',
  '517933260gunagli_v',
  'jingpingshuanghuang',
  'shangyuxiezhuang02',
  'ddddddd0000000000',
];

const ESClient = new elasticsearch.Client({
  hosts: [
    {
      host: 'es-cluster-1.impress-es.svc.cluster.local',
      port: '9200',
    },
    {
      host: 'es-cluster-2.impress-es.svc.cluster.local',
      port: '9200',
    },
    {
      host: 'es-cluster-3.impress-es.svc.cluster.local',
      port: '9200',
    },
    {
      host: 'es-cluster-4.impress-es.svc.cluster.local',
      port: '9200',
    },
    {
      host: 'es-cluster-5.impress-es.svc.cluster.local',
      port: '9200',
    },
  ],
  sniffOnStart: true,
  sniffOnConnectionFault: true,
  sniffInterval: 5000,
  requestTimeout: 60000,
  deadTimeout: 70000,
  maxRetries: 3,
  keepAlive: true,
  keepAliveInterval: 5000,
  log: {
    type: 'stdio',
    levels: ['error', 'warning'],
  },
});

const dbConfig = {
  host: '**********',
  port: 3306,
  user: 'impress',
  password: 'LLPU44dXvM',
  database: 'impress',
  connectionLimit: 8,
};

async function main() {
  const pool = mysql.createPool(dbConfig);

  try {
    const pingResponse = await ESClient.ping();
    if (!pingResponse) {
      throw new Error('无法连接到 Elasticsearch');
    }
    console.log('成功连接到 Elasticsearch');
    const size = 1000;
    let last_user_id = 0;
    do {
      const [rows] = await pool.execute('select id from users where id = 3903079 and id > ? order by id asc limit ?', [
        last_user_id,
        size,
      ]);
      for (let i = 0; i < rows.length; i++) {
        await rebuildAlbumsIndex(rows[i].id);
      }
      last_user_id = rows[rows.length - 1].id;
    } while (rows.length === size);
    console.log('Sync completed successfully');
  } catch (error) {
    console.error('Error during sync:', error);
  } finally {
    await pool.end();
  }
}

async function rebuildAlbumsIndex(userId) {
  try {
    const shard = GetShard(userId);
    let photo = 'photos';
    if (shard !== 0) {
      photo = `${photo}_${shard}`;
    }
    const photoTable = photo;

    // 先全部删除该用户的相册和图片索引
    try {
      await ESClient.deleteByQuery({
        conflicts: 'proceed', // 版本冲突仍继续
        refresh: true,
        index: 'albums', // FIXME: ES
        type: ['doc'], // FIXME: 不得删除 photo 索引，会导致 phash 被删
        body: {
          query: {
            term: { userId: userId },
          },
        },
      });
    } catch (error) {
      logger.log('error', 'admin ES rebuild error: ', error);
    }

    const promiseArr = [];
    let offset = 0;
    while (true) {
      // 批量重建用户所有相册索引
      const [albumAll] = await pool.execute(
        'select * from albums where user_id = ? and id > ? order by id asc limit 1000 order by id asc',
        [userId, offset],
      );
      if (albumAll.length == 0) {
        break;
      }
      offset = albumAll[albumAll.length - 1].id;
      const bulkOperate = [];
      for (let i = 0; i < albumAll.length; i++) {
        const album = albumAll[i];
        const albumIndex = getAlbumIndex(album);
        const metaCreate = { _index: 'albums', _type: 'doc', _id: album.id }; // FIXME: ES
        bulkOperate.push({ index: metaCreate });
        bulkOperate.push(albumIndex);
      }
      if (bulkOperate.length > 0) {
        promiseArr.push(ESClient.bulk({ body: bulkOperate }));
      }
    }

    let offsetPhoto = 0;
    let minId = 0;
    while (true) {
      // 批量重建用户所有图片索引
      const [photoAll] = await pool.execute(
        'select * from ? where user_id = ? and id > ? order by id asc limit 1000 order by id asc',
        [photoTable, userId, offset],
      );
      if (photoAll.length == 0) {
        break;
      }
      if (offsetPhoto === 0) {
        minId = photoAll[0].id;
      }

      offsetPhoto = photoAll[photoAll.length - 1].id;
      const bulkPhoto = [];
      const photoIds = [];
      for (let i = 0; i < photoAll.length; i++) {
        const photo = photoAll[i];
        photoIds.push(photo.id);
        const photoIndex = getPhotoIndex(photo);
        bulkPhoto.push({ update: { _index: photoTable, _type: 'doc', _id: photo.id } }); // FIXME: ES
        bulkPhoto.push({ doc: photoIndex, doc_as_upsert: true });
      }
      if (bulkPhoto.length > 0) {
        promiseArr.push(ESClient.bulk({ body: bulkPhoto }));
      }

      // 删除多余的 photo 索引
      try {
        await ESClient.deleteByQuery({
          conflicts: 'proceed', // 版本冲突仍继续
          refresh: true,
          index: photoTable, // FIXME: ES
          type: 'doc',
          body: {
            query: {
              bool: {
                filter: {
                  bool: {
                    must: [
                      { term: { userId: userId } },
                      {
                        range: {
                          // FIXME: 最小值与最大值之间不存在的部分
                          id: {
                            gte: photoAll[0].id,
                            lte: photoAll[photoAll.length - 1].id,
                          },
                        },
                      },
                    ],
                    must_not: {
                      terms: { id: photoIds },
                    },
                  },
                },
              },
            },
          },
        });
      } catch (error) {
        logger.log('error', '-- delete ES photos error: ', error);
      }
    }
    // 删除多余的 photo 索引
    try {
      await ESClient.deleteByQuery({
        conflicts: 'proceed', // 版本冲突仍继续
        refresh: true,
        index: photoTable, // FIXME: ES
        type: 'doc',
        body: {
          query: {
            bool: {
              filter: {
                bool: {
                  must: [
                    { term: { userId: userId } },
                    {
                      range: {
                        // FIXME: 小于最小值的部分
                        id: {
                          lt: minId,
                        },
                      },
                    },
                  ],
                },
              },
            },
          },
        },
      });
    } catch (error) {
      logger.log('error', '== delete ES photos error: ', error);
    }

    // 刷新用户个人信息
    try {
      const user = await pool.execute(
        'select id, phone, nickname, email, username, created_at from users where user_id = ?',
        [userId],
      );
      if (!user) {
        return res.status(400).json(message(10015));
      }
      const user_data = {
        createdAt: moment.unix(user.createdAt).format('YYYY-MM-DD HH:mm:ss'),
        email: user.email,
        id: user.id,
        nickname: user.nickname,
        phone: user.phone,
        username: user.username,
      };
      await ESClient.upsert('users', user.id, user_data);
    } catch (error) {
      logger.log('error', '== refresh ES user info error: ', error);
    }

    await Promise.all(promiseArr);
  } catch (err) {
    logger.log('error', 'admin ES rebuild error -- : ', err);
  }
}

function GetShard(userId) {
  userId = parseInt(userId);
  if (userNotInShardTable.has(userId)) {
    return 0;
  }

  if (userShardCalculationError.has(userId)) {
    return userShardCalculationError.get(userId);
  }

  return (userId % 32) + 1;
}

function getAlbumIndex(album) {
  if (!album.id) {
    logger.log('error', 'Album index must have id');
    return {};
  }

  const pattern = /[a-zA-Z0-9-\.]+/g;
  let name = album.name.match(pattern);
  name = name || [];
  name = name.filter((item) => item.length > 2);
  const en = name ? name.join(' ') : '';
  return {
    id: album.id,
    userId: album.userId,
    name: album.name,
    nameEN: en,
    status: album.status,
    permission: album.permission,
    description: album.description,
    createdAt: dateFormat(new Date(album.createdAt * 1000)),
    auditStatus: album.auditStatus,
  };
}

function getPhotoIndex(photo) {
  if (!photo.id) {
    logger.log('error', 'Photo index must have id');
    return {};
  }

  const pattern = /[a-zA-Z0-9-\.]+/g;
  let name = photo.name.match(pattern);
  name = name || [];
  // name = name.filter(item => item.length > 2);  // FIXME: 必须连续2个字母以上
  const en = name ? name.join(' ') : '';
  return {
    id: photo.id,
    userId: photo.userId,
    albumId: photo.albumId,
    name: photo.name,
    nameEN: en.length > 3 ? en : '',
    type: photo.type,
    status: photo.status,
    description: photo.description,
    createdAt: dateFormat(new Date(photo.createdAt * 1000)),
  };
}

function dateFormat(date = '', format = '') {
  date = date ? date : new Date();
  format = format ? format : 'yyyy-MM-dd hh:ii:ss';
  const options = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'i+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(format)) format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  for (const k in options) {
    if (new RegExp('(' + k + ')').test(format)) {
      if (RegExp.$1.length === 1) {
        format = format.replace(RegExp.$1, options[k]);
      } else {
        format = format.replace(RegExp.$1, ('00' + options[k]).substr(String(options[k]).length));
      }
    }
  }
  return format;
}

main();
